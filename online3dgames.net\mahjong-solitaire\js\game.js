/**
 * Mahjong Solitaire Game Engine
 */

class MahjongGame {
    constructor() {
        this.tileSet = new MahjongTileSet();
        this.layout = new MahjongLayout();
        this.gameBoard = null;
        this.selectedTiles = [];
        this.gameState = 'menu'; // 'menu', 'playing', 'paused', 'won', 'lost'
        this.score = 0;
        this.startTime = null;
        this.elapsedTime = 0;
        this.hintsUsed = 0;
        this.movesCount = 0;
        this.undoStack = [];
        this.currentLayout = 'turtle';
        this.debugMode = false;
        this.settings = {
            sound: true,
            hints: true,
            difficulty: 'medium'
        };
        
        this.init();
    }

    /**
     * Initialize the game
     */
    init() {
        this.gameBoard = document.getElementById('mahjongLayout');
        this.setupEventListeners();
        this.loadSettings();
        this.showLoadingScreen();
        
        // Start the game after a short delay
        setTimeout(() => {
            this.hideLoadingScreen();
            this.startNewGame();
        }, 1500);
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Game controls
        document.getElementById('hintBtn').addEventListener('click', () => this.showHint());
        document.getElementById('undoBtn').addEventListener('click', () => this.undoMove());
        document.getElementById('restartBtn').addEventListener('click', () => this.restartGame());
        document.getElementById('settingsBtn').addEventListener('click', () => this.showSettings());
        document.getElementById('rulesBtn').addEventListener('click', () => this.showRules());
        document.getElementById('fullscreenBtn').addEventListener('click', () => this.toggleFullscreen());

        // Debug mode toggle (double-click on title)
        document.querySelector('h1').addEventListener('dblclick', () => this.toggleDebugMode());
        
        // Modal controls
        document.getElementById('closeSettingsBtn').addEventListener('click', () => this.hideSettings());
        document.getElementById('closeRulesBtn').addEventListener('click', () => this.hideRules());
        document.getElementById('closeRulesOkBtn').addEventListener('click', () => this.hideRules());
        document.getElementById('applySettingsBtn').addEventListener('click', () => this.applySettings());
        document.getElementById('cancelSettingsBtn').addEventListener('click', () => this.hideSettings());
        
        // Game messages
        document.getElementById('newGameBtn').addEventListener('click', () => this.startNewGame());
        document.getElementById('closeMessageBtn').addEventListener('click', () => this.hideGameMessage());
        
        // Keyboard controls
        document.addEventListener('keydown', (e) => this.handleKeyPress(e));
        
        // Tile click handling (delegated)
        this.gameBoard.addEventListener('click', (e) => this.handleTileClick(e));

        // Touch event handling for mobile
        this.gameBoard.addEventListener('touchstart', (e) => this.handleTouchStart(e), { passive: false });
        this.gameBoard.addEventListener('touchend', (e) => this.handleTouchEnd(e), { passive: false });

        // Prevent context menu on tiles
        this.gameBoard.addEventListener('contextmenu', (e) => e.preventDefault());

        // Prevent zoom on double tap
        this.gameBoard.addEventListener('touchend', (e) => {
            if (e.touches.length > 1) {
                e.preventDefault();
            }
        });

        // Handle orientation change
        window.addEventListener('orientationchange', () => {
            setTimeout(() => this.handleOrientationChange(), 100);
        });

        // Handle resize
        window.addEventListener('resize', () => {
            setTimeout(() => this.centerLayout(), 100);
        });
    }

    /**
     * Start a new game
     */
    startNewGame() {
        this.gameState = 'playing';
        this.score = 0;
        this.startTime = Date.now();
        this.elapsedTime = 0;
        this.hintsUsed = 0;
        this.movesCount = 0;
        this.selectedTiles = [];
        this.undoStack = [];
        
        // Generate tiles and layout
        this.tileSet.generateTiles();
        this.tileSet.shuffle();
        
        // Get layout positions
        const positions = this.layout.getLayout(this.currentLayout);
        
        // Assign tiles to positions
        this.assignTilesToPositions(positions);
        
        // Render the game board
        this.renderGameBoard();
        
        // Update selectability
        this.updateTileSelectability();
        
        // Update UI
        this.updateGameStats();
        this.hideGameMessage();
        
        // Start timer
        this.startTimer();
        
        console.log(`New game started with ${this.tileSet.tiles.length} tiles in ${this.currentLayout} layout`);
    }

    /**
     * Assign tiles to layout positions
     */
    assignTilesToPositions(positions) {
        // Ensure we have enough positions for all tiles
        if (positions.length < this.tileSet.tiles.length) {
            console.warn(`Not enough positions (${positions.length}) for tiles (${this.tileSet.tiles.length})`);
        }
        
        // Assign positions to tiles
        this.tileSet.tiles.forEach((tile, index) => {
            if (index < positions.length) {
                const pos = positions[index];
                tile.setPosition(pos.x, pos.y, pos.layer);
            }
        });
    }

    /**
     * Render the game board
     */
    renderGameBoard() {
        // Clear existing tiles
        this.gameBoard.innerHTML = '';
        
        // Sort tiles by layer (bottom to top) for proper rendering
        const sortedTiles = [...this.tileSet.tiles].sort((a, b) => a.layer - b.layer);
        
        // Create and append tile elements
        sortedTiles.forEach(tile => {
            if (!tile.removed) {
                const element = tile.createElement();
                element.classList.add('appearing');
                this.gameBoard.appendChild(element);
                
                // Remove appearing animation class after animation
                setTimeout(() => {
                    element.classList.remove('appearing');
                }, 500);
            }
        });
        
        // Center the layout
        this.centerLayout();
    }

    /**
     * Center the layout on the screen
     */
    centerLayout() {
        const boardRect = this.gameBoard.getBoundingClientRect();
        const tiles = this.gameBoard.querySelectorAll('.mahjong-tile');
        
        if (tiles.length === 0) return;
        
        // Find bounds of all tiles
        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
        
        tiles.forEach(tile => {
            const x = parseInt(tile.style.left);
            const y = parseInt(tile.style.top);
            minX = Math.min(minX, x);
            minY = Math.min(minY, y);
            maxX = Math.max(maxX, x + 50); // tile width
            maxY = Math.max(maxY, y + 70); // tile height
        });
        
        // Calculate offset to center
        const layoutWidth = maxX - minX;
        const layoutHeight = maxY - minY;
        const offsetX = (boardRect.width - layoutWidth) / 2 - minX;
        const offsetY = (boardRect.height - layoutHeight) / 2 - minY;
        
        // Apply offset to all tiles
        tiles.forEach(tile => {
            const currentX = parseInt(tile.style.left);
            const currentY = parseInt(tile.style.top);
            tile.style.left = `${currentX + offsetX}px`;
            tile.style.top = `${currentY + offsetY}px`;
        });
    }

    /**
     * Update tile selectability based on game rules
     */
    updateTileSelectability() {
        this.tileSet.tiles.forEach(tile => {
            if (tile.removed) {
                tile.setSelectable(false);
                return;
            }
            
            // Check if tile is free (no tiles on top and at least one side open)
            const isFree = this.isTileFree(tile);
            tile.setSelectable(isFree);
        });
    }

    /**
     * Check if a tile is free (can be selected)
     */
    isTileFree(tile) {
        // Check if any tile is directly on top of this tile
        const hasBlockingTile = this.tileSet.tiles.some(otherTile => {
            if (otherTile.removed || otherTile.id === tile.id) return false;

            // Check if otherTile is exactly one layer above
            if (otherTile.layer !== tile.layer + 1) return false;

            // Check for significant overlap (more than 50% in both directions)
            const xOverlap = Math.abs(otherTile.x - tile.x) < this.layout.tileWidth * 0.8;
            const yOverlap = Math.abs(otherTile.y - tile.y) < this.layout.tileHeight * 0.8;

            return xOverlap && yOverlap;
        });

        if (hasBlockingTile) return false;

        // Check if at least one side is completely open
        const leftOpen = this.isSideOpen(tile, -1, 0);
        const rightOpen = this.isSideOpen(tile, 1, 0);

        return leftOpen || rightOpen;
    }

    /**
     * Check if a side of the tile is open
     */
    isSideOpen(tile, deltaX, deltaY) {
        // Check both immediate adjacent position and slightly overlapping positions
        const positions = [
            { x: tile.x + deltaX * (this.layout.tileWidth + this.layout.tileSpacing), y: tile.y },
            { x: tile.x + deltaX * this.layout.tileWidth * 0.8, y: tile.y },
            { x: tile.x + deltaX * this.layout.tileWidth * 1.2, y: tile.y }
        ];

        // Check if any of these positions have a blocking tile on the same layer
        for (const pos of positions) {
            const blockingTile = this.tileSet.tiles.find(otherTile => {
                if (otherTile.removed || otherTile.id === tile.id) return false;
                if (otherTile.layer !== tile.layer) return false;

                const xOverlap = Math.abs(otherTile.x - pos.x) < this.layout.tileWidth * 0.6;
                const yOverlap = Math.abs(otherTile.y - pos.y) < this.layout.tileHeight * 0.6;

                return xOverlap && yOverlap;
            });

            if (blockingTile) return false;
        }

        return true;
    }

    /**
     * Handle tile click
     */
    handleTileClick(event) {
        if (this.gameState !== 'playing') return;
        
        const tileElement = event.target.closest('.mahjong-tile');
        if (!tileElement) return;
        
        const tileId = parseInt(tileElement.dataset.tileId);
        const tile = this.tileSet.getTileById(tileId);
        
        if (!tile || !tile.selectable || tile.removed) return;
        
        this.selectTile(tile);
    }

    /**
     * Select a tile
     */
    selectTile(tile) {
        if (tile.selected) {
            // Deselect tile
            tile.setSelected(false);
            this.selectedTiles = this.selectedTiles.filter(t => t.id !== tile.id);
            return;
        }
        
        // Select tile
        tile.setSelected(true);
        this.selectedTiles.push(tile);
        
        // Check if we have two selected tiles
        if (this.selectedTiles.length === 2) {
            this.checkMatch();
        } else if (this.selectedTiles.length > 2) {
            // Deselect all but the last selected tile
            this.selectedTiles.slice(0, -1).forEach(t => t.setSelected(false));
            this.selectedTiles = [tile];
        }
    }

    /**
     * Check if selected tiles match
     */
    checkMatch() {
        if (this.selectedTiles.length !== 2) return;
        
        const [tile1, tile2] = this.selectedTiles;
        
        if (tile1.canMatch(tile2)) {
            // Match found!
            this.removeMatchedTiles(tile1, tile2);
        } else {
            // No match, deselect tiles after a brief delay
            setTimeout(() => {
                tile1.setSelected(false);
                tile2.setSelected(false);
                this.selectedTiles = [];
            }, 500);
        }
    }

    /**
     * Remove matched tiles
     */
    removeMatchedTiles(tile1, tile2) {
        // Add to undo stack
        this.undoStack.push({
            tile1: { ...tile1 },
            tile2: { ...tile2 },
            score: this.score,
            movesCount: this.movesCount
        });
        
        // Add matching animation
        tile1.element.classList.add('matching');
        tile2.element.classList.add('matching');
        
        setTimeout(() => {
            // Remove tiles
            tile1.remove();
            tile2.remove();
            
            // Update game state
            this.selectedTiles = [];
            this.movesCount++;
            this.score += 10;
            
            // Update selectability
            this.updateTileSelectability();
            
            // Update UI
            this.updateGameStats();
            
            // Check win condition
            this.checkWinCondition();
            
            // Check if moves are available
            if (!this.tileSet.hasValidMoves() && !this.isGameWon()) {
                this.gameOver(false);
            }
        }, 300);
    }

    /**
     * Show hint
     */
    showHint() {
        if (this.gameState !== 'playing' || !this.settings.hints) return;
        
        const hint = this.tileSet.getHint();
        if (hint) {
            this.hintsUsed++;
            this.score = Math.max(0, this.score - 5);
            
            // Highlight hint tiles
            hint.forEach(tile => {
                tile.element.classList.add('hint');
                setTimeout(() => {
                    tile.element.classList.remove('hint');
                }, 3000);
            });
            
            this.updateGameStats();
        } else {
            this.showGameMessage('No Moves Available', 'There are no more valid moves. Try restarting the game.');
        }
    }

    /**
     * Undo last move
     */
    undoMove() {
        if (this.gameState !== 'playing' || this.undoStack.length === 0) return;
        
        const lastMove = this.undoStack.pop();
        
        // Restore tiles
        const tile1 = this.tileSet.getTileById(lastMove.tile1.id);
        const tile2 = this.tileSet.getTileById(lastMove.tile2.id);
        
        tile1.removed = false;
        tile1.selected = false;
        tile2.removed = false;
        tile2.selected = false;
        
        // Recreate elements
        const element1 = tile1.createElement();
        const element2 = tile2.createElement();
        element1.classList.add('appearing');
        element2.classList.add('appearing');
        
        this.gameBoard.appendChild(element1);
        this.gameBoard.appendChild(element2);
        
        // Restore game state
        this.score = lastMove.score;
        this.movesCount = lastMove.movesCount;
        this.selectedTiles = [];
        
        // Update selectability and UI
        this.updateTileSelectability();
        this.updateGameStats();
        
        setTimeout(() => {
            element1.classList.remove('appearing');
            element2.classList.remove('appearing');
        }, 500);
    }

    /**
     * Check win condition
     */
    checkWinCondition() {
        if (this.isGameWon()) {
            this.gameOver(true);
        }
    }

    /**
     * Check if game is won
     */
    isGameWon() {
        return this.tileSet.tiles.every(tile => tile.removed);
    }

    /**
     * Game over
     */
    gameOver(won) {
        this.gameState = won ? 'won' : 'lost';
        this.stopTimer();
        
        const timeBonus = won ? Math.max(0, 1000 - Math.floor(this.elapsedTime / 1000)) : 0;
        this.score += timeBonus;
        
        const title = won ? 'Congratulations!' : 'Game Over';
        const message = won 
            ? `You completed the game in ${this.formatTime(this.elapsedTime)}!\nFinal Score: ${this.score}\nMoves: ${this.movesCount}\nHints Used: ${this.hintsUsed}`
            : 'No more moves available. Better luck next time!';
        
        this.showGameMessage(title, message);
    }

    /**
     * Update game statistics display
     */
    updateGameStats() {
        document.getElementById('timeDisplay').textContent = `Time: ${this.formatTime(this.elapsedTime)}`;
        document.getElementById('scoreDisplay').textContent = `Score: ${this.score}`;
        
        const stats = this.tileSet.getStats();
        document.getElementById('tilesLeft').textContent = `Tiles: ${stats.remaining}`;
        
        // Update button states
        document.getElementById('undoBtn').disabled = this.undoStack.length === 0;
        document.getElementById('hintBtn').disabled = !this.settings.hints || !this.tileSet.hasValidMoves();
    }

    /**
     * Format time for display
     */
    formatTime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    /**
     * Start timer
     */
    startTimer() {
        this.timerInterval = setInterval(() => {
            if (this.gameState === 'playing') {
                this.elapsedTime = Date.now() - this.startTime;
                this.updateGameStats();
            }
        }, 1000);
    }

    /**
     * Stop timer
     */
    stopTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
    }

    /**
     * Show loading screen
     */
    showLoadingScreen() {
        document.getElementById('loadingScreen').classList.remove('hidden');
    }

    /**
     * Hide loading screen
     */
    hideLoadingScreen() {
        document.getElementById('loadingScreen').classList.add('hidden');
    }

    /**
     * Show game message
     */
    showGameMessage(title, message) {
        document.getElementById('messageTitle').textContent = title;
        document.getElementById('messageText').textContent = message;
        document.getElementById('gameMessage').style.display = 'flex';
    }

    /**
     * Hide game message
     */
    hideGameMessage() {
        document.getElementById('gameMessage').style.display = 'none';
    }

    /**
     * Show settings modal
     */
    showSettings() {
        document.getElementById('layoutSelect').value = this.currentLayout;
        document.getElementById('difficultySelect').value = this.settings.difficulty;
        document.getElementById('soundToggle').checked = this.settings.sound;
        document.getElementById('hintsToggle').checked = this.settings.hints;
        document.getElementById('settingsModal').style.display = 'flex';
    }

    /**
     * Hide settings modal
     */
    hideSettings() {
        document.getElementById('settingsModal').style.display = 'none';
    }

    /**
     * Apply settings
     */
    applySettings() {
        this.currentLayout = document.getElementById('layoutSelect').value;
        this.settings.difficulty = document.getElementById('difficultySelect').value;
        this.settings.sound = document.getElementById('soundToggle').checked;
        this.settings.hints = document.getElementById('hintsToggle').checked;
        
        this.saveSettings();
        this.hideSettings();
        
        // Restart game with new settings
        this.startNewGame();
    }

    /**
     * Show rules modal
     */
    showRules() {
        document.getElementById('rulesModal').style.display = 'flex';
    }

    /**
     * Hide rules modal
     */
    hideRules() {
        document.getElementById('rulesModal').style.display = 'none';
    }

    /**
     * Restart game
     */
    restartGame() {
        this.startNewGame();
    }

    /**
     * Toggle fullscreen
     */
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    /**
     * Handle keyboard input
     */
    handleKeyPress(event) {
        switch (event.key) {
            case 'h':
            case 'H':
                this.showHint();
                break;
            case 'u':
            case 'U':
                this.undoMove();
                break;
            case 'r':
            case 'R':
                this.restartGame();
                break;
            case 'Escape':
                this.hideSettings();
                this.hideRules();
                this.hideGameMessage();
                break;
            case 'F11':
                event.preventDefault();
                this.toggleFullscreen();
                break;
        }
    }

    /**
     * Load settings from localStorage
     */
    loadSettings() {
        const saved = localStorage.getItem('mahjongSettings');
        if (saved) {
            this.settings = { ...this.settings, ...JSON.parse(saved) };
        }
    }

    /**
     * Save settings to localStorage
     */
    saveSettings() {
        localStorage.setItem('mahjongSettings', JSON.stringify(this.settings));
    }

    /**
     * Handle touch start
     */
    handleTouchStart(event) {
        event.preventDefault();
        this.touchStartTime = Date.now();
        this.touchStartTarget = event.target;
    }

    /**
     * Handle touch end
     */
    handleTouchEnd(event) {
        event.preventDefault();

        // Only process if touch duration is reasonable (not a long press)
        const touchDuration = Date.now() - this.touchStartTime;
        if (touchDuration > 500) return;

        // Only process if touch ended on the same element
        if (event.target !== this.touchStartTarget) return;

        // Simulate click event
        this.handleTileClick({ target: event.target });
    }

    /**
     * Handle orientation change
     */
    handleOrientationChange() {
        // Re-center layout after orientation change
        this.centerLayout();

        // Show orientation hint for mobile
        if (window.innerWidth < 768) {
            const isLandscape = window.innerWidth > window.innerHeight;
            if (!isLandscape && this.gameState === 'playing') {
                // Suggest landscape mode for better experience
                this.showOrientationHint();
            }
        }
    }

    /**
     * Show orientation hint
     */
    showOrientationHint() {
        const hint = document.createElement('div');
        hint.className = 'orientation-hint';
        hint.innerHTML = `
            <div class="hint-content">
                <div class="hint-icon">📱</div>
                <p>Rotate your device for better experience</p>
            </div>
        `;
        hint.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            color: white;
            text-align: center;
        `;

        document.body.appendChild(hint);

        // Auto-hide after 3 seconds
        setTimeout(() => {
            if (hint.parentNode) {
                hint.parentNode.removeChild(hint);
            }
        }, 3000);

        // Hide on tap
        hint.addEventListener('click', () => {
            if (hint.parentNode) {
                hint.parentNode.removeChild(hint);
            }
        });
    }

    /**
     * Check if device is mobile
     */
    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               (window.innerWidth <= 768);
    }

    /**
     * Optimize for mobile performance
     */
    optimizeForMobile() {
        if (this.isMobile()) {
            // Reduce animation complexity on mobile
            document.body.classList.add('mobile-device');

            // Add viewport meta tag if not present
            if (!document.querySelector('meta[name="viewport"]')) {
                const viewport = document.createElement('meta');
                viewport.name = 'viewport';
                viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
                document.head.appendChild(viewport);
            }
        }
    }

    /**
     * Toggle debug mode
     */
    toggleDebugMode() {
        this.debugMode = !this.debugMode;

        if (this.debugMode) {
            document.body.classList.add('debug-mode');
            this.showDebugInfo();
        } else {
            document.body.classList.remove('debug-mode');
            this.hideDebugInfo();
        }
    }

    /**
     * Show debug information
     */
    showDebugInfo() {
        this.tileSet.tiles.forEach(tile => {
            if (tile.element && !tile.removed) {
                // Add layer indicator
                const layerIndicator = document.createElement('div');
                layerIndicator.className = 'debug-layer-indicator';
                layerIndicator.textContent = tile.layer;
                layerIndicator.style.cssText = `
                    position: absolute;
                    top: -8px;
                    right: -8px;
                    width: 16px;
                    height: 16px;
                    background: red;
                    color: white;
                    font-size: 10px;
                    font-weight: bold;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 1000;
                    pointer-events: none;
                `;
                tile.element.appendChild(layerIndicator);

                // Add coordinate info
                const coordIndicator = document.createElement('div');
                coordIndicator.className = 'debug-coord-indicator';
                coordIndicator.textContent = `${tile.x},${tile.y}`;
                coordIndicator.style.cssText = `
                    position: absolute;
                    bottom: -15px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: blue;
                    color: white;
                    font-size: 8px;
                    padding: 1px 3px;
                    border-radius: 3px;
                    z-index: 1000;
                    pointer-events: none;
                    white-space: nowrap;
                `;
                tile.element.appendChild(coordIndicator);
            }
        });
    }

    /**
     * Hide debug information
     */
    hideDebugInfo() {
        document.querySelectorAll('.debug-layer-indicator, .debug-coord-indicator').forEach(el => {
            el.remove();
        });
    }
}

// Initialize game when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.mahjongGame = new MahjongGame();
});
