<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mahjong Solitaire - Free Online Game | Online3DGames.net</title>
    <meta name="description" content="Play Mahjong Solitaire online for free. Classic tile-matching puzzle game with beautiful 3D tiles and multiple layouts. No download required!">
    <meta name="keywords" content="mahjong solitaire, tile matching, puzzle game, free online game, mahjong tiles">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Mahjong Solitaire - Free Online Game">
    <meta property="og:description" content="Play Mahjong Solitaire online for free. Classic tile-matching puzzle game with beautiful 3D tiles and multiple layouts.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://online3dgames.net/mahjong-solitaire/">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Mahjong Solitaire - Free Online Game">
    <meta name="twitter:description" content="Play Mahjong Solitaire online for free. Classic tile-matching puzzle game with beautiful 3D tiles and multiple layouts.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../favicon.ico">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/common-buttons.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/mahjong.css">
    
    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-49EMLQ4Q49"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-49EMLQ4Q49');
    </script>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-content">
            <div class="loader"></div>
            <div class="loading">Loading Mahjong Solitaire...</div>
        </div>
    </div>

    <!-- Game Container -->
    <div class="game-container" id="gameContainer">
        <!-- Game Header -->
        <div class="game-header">
            <div class="header-left">
                <h1>🀄 Mahjong Solitaire</h1>
                <div class="game-stats">
                    <span id="timeDisplay">Time: 00:00</span>
                    <span id="scoreDisplay">Score: 0</span>
                    <span id="tilesLeft">Tiles: 144</span>
                </div>
            </div>
            <div class="header-right">
                <button class="btn btn-hint" id="hintBtn" title="Show Hint">💡</button>
                <button class="btn btn-undo" id="undoBtn" title="Undo Last Move">↶</button>
                <button class="btn btn-restart" id="restartBtn" title="Restart Game">🔄</button>
                <button class="btn btn-settings" id="settingsBtn" title="Settings">⚙️</button>
                <button class="btn btn-rules" id="rulesBtn" title="Game Rules">❓</button>
                <button class="btn btn-fullscreen" id="fullscreenBtn" title="Toggle Fullscreen">⛶</button>
            </div>
        </div>

        <!-- Game Board -->
        <div class="game-board" id="gameBoard">
            <div class="mahjong-layout" id="mahjongLayout">
                <!-- Tiles will be dynamically generated here -->
            </div>
        </div>

        <!-- Game Messages -->
        <div class="game-message" id="gameMessage" style="display: none;">
            <div class="message-content">
                <h2 id="messageTitle">Game Over</h2>
                <p id="messageText">Congratulations! You completed the game!</p>
                <div class="message-buttons">
                    <button class="btn btn-primary" id="newGameBtn">New Game</button>
                    <button class="btn btn-secondary" id="closeMessageBtn">Close</button>
                </div>
            </div>
        </div>

        <!-- Settings Modal -->
        <div class="modal" id="settingsModal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Settings</h3>
                    <button class="modal-close" id="closeSettingsBtn">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="setting-group">
                        <label>Layout:</label>
                        <select id="layoutSelect">
                            <option value="turtle">Turtle</option>
                            <option value="pyramid">Pyramid</option>
                            <option value="cross">Cross</option>
                            <option value="spider">Spider</option>
                        </select>
                    </div>
                    <div class="setting-group">
                        <label>Difficulty:</label>
                        <select id="difficultySelect">
                            <option value="easy">Easy</option>
                            <option value="medium">Medium</option>
                            <option value="hard">Hard</option>
                        </select>
                    </div>
                    <div class="setting-group">
                        <label>Sound Effects:</label>
                        <input type="checkbox" id="soundToggle" checked>
                    </div>
                    <div class="setting-group">
                        <label>Show Hints:</label>
                        <input type="checkbox" id="hintsToggle" checked>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" id="applySettingsBtn">Apply</button>
                    <button class="btn btn-secondary" id="cancelSettingsBtn">Cancel</button>
                </div>
            </div>
        </div>

        <!-- Rules Modal -->
        <div class="modal" id="rulesModal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>How to Play Mahjong Solitaire</h3>
                    <button class="modal-close" id="closeRulesBtn">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="rules-content">
                        <h4>Objective:</h4>
                        <p>Remove all tiles from the board by matching pairs of identical tiles.</p>
                        
                        <h4>Rules:</h4>
                        <ul>
                            <li>Only tiles that are "free" can be selected</li>
                            <li>A tile is free if it has no tiles on top and at least one side is open</li>
                            <li>Match two identical tiles to remove them from the board</li>
                            <li>Some special tiles can match with any tile of the same type</li>
                            <li>The game is won when all tiles are removed</li>
                        </ul>
                        
                        <h4>Scoring:</h4>
                        <ul>
                            <li>Each pair removed: +10 points</li>
                            <li>Time bonus: Faster completion = higher score</li>
                            <li>Hint penalty: -5 points per hint used</li>
                        </ul>
                        
                        <h4>Tips:</h4>
                        <ul>
                            <li>Look for tiles that are blocking many others</li>
                            <li>Try to free up tiles in the center first</li>
                            <li>Use the hint button if you get stuck</li>
                            <li>Plan your moves to avoid dead ends</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" id="closeRulesOkBtn">Got it!</button>
                </div>
            </div>
        </div>
    </div>

    <!-- SEO Content Section -->
    <div class="seo-content-section">
        <div class="container">
            <section class="game-description">
                <h2>About Mahjong Solitaire</h2>
                <p>Mahjong Solitaire is a classic tile-matching puzzle game that challenges your pattern recognition and strategic thinking. Unlike traditional Mahjong, this is a single-player game where you remove pairs of matching tiles from a carefully arranged layout.</p>
                
                <h3>Game Features</h3>
                <ul>
                    <li><strong>Multiple Layouts:</strong> Choose from classic layouts like Turtle, Pyramid, Cross, and Spider</li>
                    <li><strong>Beautiful 3D Tiles:</strong> Authentic-looking mahjong tiles with traditional symbols</li>
                    <li><strong>Hint System:</strong> Get help when you're stuck with our intelligent hint system</li>
                    <li><strong>Undo Function:</strong> Correct mistakes with the undo feature</li>
                    <li><strong>Score Tracking:</strong> Compete against yourself with time-based scoring</li>
                    <li><strong>Mobile Friendly:</strong> Play on any device with responsive design</li>
                </ul>
                
                <h3>Why Play Mahjong Solitaire?</h3>
                <p>This timeless puzzle game offers the perfect balance of relaxation and mental stimulation. It's an excellent way to improve concentration, pattern recognition, and strategic planning skills while enjoying the beautiful aesthetics of traditional mahjong tiles.</p>
            </section>
        </div>
    </div>

    <!-- Game Recommendations -->
    <div class="game-recommendations similar-games">
        <div class="container">
            <h2>Similar Puzzle Games</h2>
            <div class="recommendations-grid">
                <a href="../spider-solitaire/" class="recommendation-card">
                    <div class="card-image">
                        <div class="game-icon">🕷️</div>
                        <h4 class="game-name">Spider Solitaire</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="../freecell-solitaire/" class="recommendation-card">
                    <div class="card-image">
                        <div class="game-icon">🃏</div>
                        <h4 class="game-name">FreeCell Solitaire</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="../klondike-solitaire/" class="recommendation-card">
                    <div class="card-image">
                        <div class="game-icon">♠️</div>
                        <h4 class="game-name">Klondike Solitaire</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="../sudoku-online/" class="recommendation-card">
                    <div class="card-image">
                        <div class="game-icon">🧩</div>
                        <h4 class="game-name">Sudoku</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="../assets/js/main.js"></script>
    <script src="js/mahjong.js"></script>
    <script src="js/game.js"></script>
</body>
</html>
